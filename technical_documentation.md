# Windows工具箱 - 技术设计文档

**版本**: 1.0
**日期**: 2025-07-30

[TOC]

## 1. 概述

### 1.1 项目目标

本文档旨在为 "Windows工具箱" 项目提供一份全面的技术设计和实现指南。该项目致力于开发一款集硬件信息查看、系统优化清理、预装应用卸载、超频工具管理及常用快捷工具于一体的现代化Windows桌面应用。

本文档将详细阐述项目的技术架构、模块划分、UI设计、核心功能实现方案以及关键技术点的决策，旨在确保开发过程的规范性、代码的可维护性和未来的可扩展性。

### 1.2 技术选型

为确保应用界面的现代化、用户体验的流畅性以及功能的稳定性，项目选用以下技术栈：

*   **编程语言**: **Python 3.x**
*   **UI 框架**:
    *   **PySide6**: Qt for Python的官方绑定，提供强大的桌面应用开发能力。
    *   **PySide6-Fluent-Widgets**: 一个基于PySide6的精美Fluent Design组件库，用于构建现代化、符合Windows 11设计风格的UI。
*   **硬件信息**: **WMI (Windows Management Instrumentation)**
    *   通过 `wmi` Python库访问，能够全面、准确地获取Windows系统的底层硬件和系统信息。
*   **系统交互**:
    *   **pywin32**: 用于访问Windows原生API的Python扩展。
    *   **ctypes**: Python标准库，用于调用C动态链接库，本项目中主要用于权限检测与提升。

---

## 2. 系统架构

### 2.1 总体分层设计

项目采用分层架构，将UI、业务逻辑和数据配置清晰地分离开来，以实现高内聚、低耦合的设计目标。

```mermaid
graph TD
    subgraph UI层
        A[主窗口 MainWindow]
        B[硬件信息视图 HardwareView]
        C[优化清理视图 CleanupView]
        D[...]
    end

    subgraph 服务层 (业务逻辑)
        E[硬件服务 HardwareService]
        F[清理服务 CleanupService]
        G[应用卸载服务 AppxService]
        H[...]
    end

    subgraph 核心工具/数据层
        I[权限管理器 PrivilegeManager]
        J[进程执行器 ProcessRunner]
        K[异步工作线程 BaseWorker]
        L[配置文件 Config Files]
    end

    A --> B
    A --> C
    A --> D

    B -- 请求/更新 --> E
    C -- 执行任务 --> F
    
    E -- 异步执行 --> K
    F -- 异步执行 --> K

    E -- 读取硬件 --> WMI
    F -- 执行命令 --> J
    G -- 执行命令 --> J

    J -- 检查/提升权限 --> I
    
    subgraph 外部/系统
        WMI
    end

    E -- 读取数据 --> L
    F -- 读取数据 --> L
    G -- 读取数据 --> L
```

*   **UI层 (Interfaces)**: 负责界面的展示和用户交互。由`FluentWindow`作为主框架，加载各个功能视图（如硬件信息、优化清理等）。视图本身只负责UI元素的布局和事件的发出，不包含具体的业务逻辑。
*   **服务层 (Services)**: 负责处理核心业务逻辑。每个主要功能模块都有一个对应的服务类（如`HardwareService`）。服务类接收来自UI层的请求，执行相应的操作（如查询WMI、执行脚本），并通过信号将结果返回给UI层。
*   **核心工具与数据层 (Utils / Config)**:
    *   **Utils**: 提供通用的辅助功能，如管理员权限检测与提升 (`PrivilegeManager`)、安全执行外部进程 (`ProcessRunner`)。
    *   **Workers**: 提供异步执行任务的基类 (`QThread`)，防止UI线程阻塞。
    *   **Config**: 存放所有功能的静态配置文件，如PowerShell命令、注册表项、应用列表等。服务层按需读取这些配置。

### 2.2 目录结构

为支持上述分层架构，项目将采用以下目录结构：

```
Windows工具箱/
├── main.py                 # 应用程序入口，负责初始化和启动
├── requirements.txt        # 项目依赖
├── README.md               # 项目说明
├── logs.md                 # 修改日志
|
├── assets/                 # 存放图标、图片等静态资源
│   ├── icon.ico
│   └── ...
|
├── config/                 # 功能配置文件
│   ├── appx_packages.py
│   ├── powershell_commands.py
│   └── ...
|
├── interfaces/             # UI视图模块
│   ├── __init__.py
│   ├── main_window.py      # 主窗口实现
│   ├── hardware_view.py    # 硬件信息视图
│   ├── cleanup_view.py     # 优化清理视图
│   └── ...
|
├── services/               # 业务逻辑服务模块
│   ├── __init__.py
│   ├── hardware_service.py # 硬件信息服务
│   ├── cleanup_service.py  # 清理服务
│   └── ...
|
├── utils/                  # 通用工具模块
│   ├── __init__.py
│   ├── privilege_manager.py# 权限管理
│   ├── config_loader.py    # 配置加载器
│   └── process_runner.py   # 进程执行器
|
└── workers/                # 异步任务工作线程
    ├── __init__.py
    └── base_worker.py      # 通用QThread工作线程基类
```

---

## 3. UI层设计

本章节详细描述应用程序的用户界面设计，所有组件实现将严格遵循 `PySide6-Fluent-Widgets` 官方文档，仅在功能不满足时才使用 `PySide6` 原生组件。

### 3.1 主窗口 (`interfaces/main_window.py`)

*   **基类**: `qfluentwidgets.FluentWindow`。
*   **布局**: 采用库自带的左侧导航栏和右侧堆叠窗口 (`QStackedWidget`) 布局。
*   **导航**:
    *   使用 `self.navigationInterface.addSubInterface(widget, icon, text)` 方法添加顶部功能视图。
    *   使用 `self.navigationInterface.addSeparator()` 在导航项之间添加分隔符。
    *   使用 `self.navigationInterface.addSubInterface(..., position=NavigationItemPosition.BOTTOM)` 将“设置”和“关于”视图添加到导航栏底部。
*   **窗口属性**:
    *   在 `main.py` 中设置窗口图标、标题和应用程序模型ID。
    *   `self.setWindowIcon(QIcon(":/assets/icon.ico"))`
    *   `self.setWindowTitle("Windows 工具箱")`

### 3.2 启动页面 (`main.py`)

*   **组件**: `qfluentwidgets.SplashScreen`。
*   **实现流程**:
    1.  在 `main` 函数中，首先创建主窗口 `Window` 的实例。
    2.  创建 `SplashScreen(window.windowIcon(), window)`，并设置其尺寸与主窗口一致。
    3.  显示主窗口 `window.show()`。
    4.  显示启动页 `splashScreen.show()`。
    5.  在主窗口中执行耗时的初始化操作（如创建子界面）。
    6.  初始化完成后，调用 `splashScreen.finish()` 来关闭启动页。

### 3.3 硬件信息视图 (`interfaces/hardware_view.py`)

*   **根组件**: `QScrollArea`，确保内容可滚动。
*   **布局**: `QVBoxLayout` 垂直排列各个硬件信息卡片。
*   **信息展示**:
    *   每个硬件类别（系统、主板、处理器等）使用一个 `qfluentwidgets.HeaderCardWidget` 或 `qfluentwidgets.CardWidget` 进行展示。
    *   卡片内部使用 `QFormLayout` 或 `QHBoxLayout` + `QLabel` / `qfluentwidgets.BodyLabel` 来展示键值对信息。
*   **异步加载**: 视图初始化时显示一个加载动画（如 `qfluentwidgets.IndeterminateProgressRing`），并触发 `HardwareService` 开始异步获取数据。数据返回后，更新UI并隐藏加载动画。
*   **一键复制**:
    *   在视图右上角放置一个 `qfluentwidgets.PushButton` 或 `qfluentwidgets.HyperlinkLabel`。
    *   点击后，调用服务层方法格式化所有硬件信息为字符串，并复制到系统剪贴板。

### 3.4 优化清理与预装卸载视图 (`interfaces/cleanup_view.py`, `interfaces/appx_view.py`)

这两个视图的结构和组件选用高度相似。

*   **选项卡**: 使用 `qfluentwidgets.Pivot` 组件实现顶部分类切换。每个 `Pivot` 项关联一个 `QWidget` 作为其页面。
*   **内容区域**: 每个页面内放置一个 `QScrollArea`，`ScrollArea` 的内容是一个 `QWidget`，该 `QWidget` 使用 `QVBoxLayout` 布局所有的选项。
*   **选项**:
    *   每个可执行的任务项使用 `qfluentwidgets.CheckBox` 或原生 `QCheckBox` 表示。
    *   对于具有父子关系的选项，通过代码逻辑实现联动：
        *   父→子：父`CheckBox`的 `stateChanged` 信号连接到一个槽函数，该函数设置所有子`CheckBox`的状态。
        *   子→父：所有子`CheckBox`的 `stateChanged` 信号连接到同一个槽函数，该函数检查所有子项的状态，并相应地设置父项的状态（`Qt.Checked`, `Qt.Unchecked`, `Qt.PartiallyChecked`）。
*   **操作按钮**:
    *   视图底部或右侧放置 "执行选中任务" (`PushButton`) 和 "一键执行所有任务" (`PrimaryPushButton`)。
    *   按钮的 `clicked` 信号连接到槽函数，该函数收集所有选中的 `CheckBox` 对应的数据，并传递给相应的服务层执行。

### 3.5 超频工具与快捷工具视图 (`interfaces/octools_view.py`, `interfaces/quicktools_view.py`)

这两个视图的布局和交互模式相似。

*   **布局**: `qfluentwidgets.FlowLayout`，使得工具卡片能够自适应窗口大小流动排列。
*   **工具卡片**:
    *   创建一个继承自 `qfluentwidgets.CardWidget` 或 `QWidget` 的自定义卡片类。
    *   卡片内部包含 `qfluentwidgets.IconWidget` 用于显示图标和 `qfluentwidgets.BodyLabel` 用于显示工具名称。
    *   为整个卡片重写 `mousePressEvent`，使其在被点击时触发相应的动作（执行命令）。
*   **二次确认**:
    *   对于高风险操作（如重启到BIOS、关机、进入安全模式），在执行前调用 `qfluentwidgets.MessageBox` 或 `qfluentwidgets.Dialog` 显示确认对话框。
    *   示例: `w = MessageBox('标题', '内容', self); w.yesButton.setText('确认'); w.cancelButton.setText('取消'); if w.exec(): ...`

### 3.6 软件设置视图 (`interfaces/settings_view.py`)

*   **布局**: 使用 `QScrollArea` 包含 `qfluentwidgets.SettingCardGroup`。
*   **主题设置**:
    *   使用 `qfluentwidgets.ComboBoxSettingCard`，选项为“浅色”、“深色”、“跟随系统”。
    *   其 `currentTextChanged` 或 `currentIndexChanged` 信号连接到槽函数，调用 `qfluentwidgets.setTheme()` 来切换应用主题。
*   **赞助作者**:
    *   使用 `qfluentwidgets.SettingCard` 或 `HyperlinkCard`，点击后弹出一个自定义对话框。
    *   对话框使用 `qfluentwidgets.Dialog` 实现，内部包含一个 `qfluentwidgets.SwitchButton` 用于切换支付宝/微信二维码，以及一个 `qfluentwidgets.ImageLabel` 用于显示对应的二维码图片。
*   **检查更新**:
    *   使用 `qfluentwidgets.HyperlinkCard`，并设置其 `content` 为项目的发布页面URL。

### 3.7 关于软件视图 (`interfaces/about_view.py`)

*   **布局**: 简单的 `QVBoxLayout`。
*   **内容**:
    *   顶部使用 `qfluentwidgets.IconWidget` 显示应用图标。
    *   使用 `TitleLabel`, `BodyLabel`, `CaptionLabel` 显示应用名称、版本号和版权信息。
    *   使用 `qfluentwidgets.HyperlinkCard` 提供跳转到抖音主页和QQ群的链接。

---

## 4. 服务层设计

服务层是应用程序的核心，负责实现所有业务逻辑。每个服务都应设计为可独立测试的模块，并通过信号与UI层进行解耦通信。

### 4.1 硬件信息服务 (`services/hardware_service.py`)

*   **类名**: `HardwareService(QObject)`
*   **核心库**: `wmi`
*   **职责**: 提供获取各种硬件信息的方法，所有方法都应在工作线程中执行。
*   **信号**:
    *   `info_updated = pyqtSignal(dict)`: 当一组硬件信息（如CPU信息）获取完毕时，发射此信号，参数为包含信息的字典。
    *   `all_info_updated = pyqtSignal(dict)`: 所有硬件信息都获取完毕后发射此信号。
    *   `error_occurred = pyqtSignal(str)`: 发生错误时发射。
*   **主要方法**:
    *   `get_all_info()`: 启动一个工作线程来依次调用下面的所有 `get_*_info` 方法。
    *   `get_system_info()`: 查询 `Win32_OperatingSystem`。
    *   `get_motherboard_info()`: 查询 `Win32_BaseBoard`, `Win32_BIOS`。
    *   `get_processor_info()`: 查询 `Win32_Processor`。
    *   `get_memory_info()`: 查询 `Win32_PhysicalMemory`。
    *   `get_graphics_card_info()`: 查询 `Win32_VideoController`。
    *   `get_disk_info()`: 查询 `Win32_DiskDrive`, `Win32_LogicalDisk`。

### 4.2 清理服务 (`services/cleanup_service.py`)

*   **类名**: `CleanupService(QObject)`
*   **职责**: 根据UI层传递的任务列表，执行系统优化和清理。
*   **信号**:
    *   `task_started = pyqtSignal(str)`: 某个任务开始执行时发射。
    *   `task_finished = pyqtSignal(str, bool)`: 某个任务完成时发射，参数为任务名和是否成功。
    *   `all_tasks_finished = pyqtSignal()`: 所有任务完成后发射。
*   **主要方法**:
    *   `execute_tasks(tasks: dict)`:
        *   接收一个结构化的字典，如 `{'powershell': [...], 'registry': [...], 'cleanup': [...]}`。
        *   启动工作线程，在线程中遍历任务列表。
        *   对每个任务，调用 `utils.process_runner` 执行相应的命令。
        *   执行完成后，调用 `_restart_explorer()` 和 `_clear_icon_cache()`。
    *   `_restart_explorer()`: 执行 `taskkill /f /im explorer.exe && start explorer.exe`。
    *   `_clear_icon_cache()`: 删除 `IconCache.db` 文件。

### 4.3 应用卸载服务 (`services/appx_service.py`)

*   **类名**: `AppxService(QObject)`
*   **职责**: 卸载用户选中的预装应用。
*   **信号**:
    *   `uninstall_progress = pyqtSignal(str)`: 报告当前正在卸载的应用。
    *   `uninstall_finished = pyqtSignal()`: 所有卸载任务完成。
*   **主要方法**:
    *   `uninstall_apps(apps_to_uninstall: list)`:
        *   接收一个包含应用包名的列表。
        *   启动工作线程，在线程中遍历列表。
        *   对每个应用，构建并执行 `Get-AppxPackage *{app_name}* | Remove-AppxPackage` PowerShell命令。

### 4.4 工具执行服务 (`services/tool_runner_service.py`)

*   **类名**: `ToolRunnerService(QObject)`
*   **职责**: 启动超频工具和快捷工具。
*   **主要方法**:
    *   `run_tool(command: str)`:
        *   接收一个命令字符串。
        *   调用 `utils.process_runner` 在子进程中执行该命令。
        *   对于GUI程序和控制台程序，需要加以区分并以合适的方式启动（`subprocess.CREATE_NEW_CONSOLE` 或 `subprocess.CREATE_NO_WINDOW`）。

---

## 5. 核心工具与工作线程

### 5.1 权限管理器 (`utils/privilege_manager.py`)

*   **模块功能**: 提供Windows管理员权限的检测和提升功能。
*   **核心库**: `ctypes`, `sys`
*   **函数**:
    *   `is_admin() -> bool`:
        *   调用 `ctypes.windll.shell32.IsUserAnAdmin()`。
        *   返回 `True` 如果当前是管理员，否则返回 `False`。
    *   `elevate()`:
        *   检查 `is_admin()`，如果已经是管理员则直接返回。
        *   否则，调用 `ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, " ".join(sys.argv), None, 1)`。
        *   这会触发UAC弹窗，请求用户授权以管理员身份重新运行当前脚本。
*   **调用时机**: 在 `main.py` 的程序入口处立即调用 `elevate()`，确保整个应用程序在管理员权限下运行。

### 5.2 进程执行器 (`utils/process_runner.py`)

*   **模块功能**: 封装 `subprocess` 模块，提供一个安全、统一的接口来执行外部命令。
*   **核心库**: `subprocess`
*   **函数**:
    *   `run_command(command: list, capture_output=False) -> subprocess.CompletedProcess`:
        *   使用 `subprocess.run()` 执行命令。
        *   设置 `shell=False`（除非命令必须在shell中执行）以避免安全风险。
        *   使用 `creationflags` 参数来控制窗口的显示（例如 `subprocess.CREATE_NO_WINDOW`）。
        *   返回 `CompletedProcess` 对象，其中包含返回码、stdout和stderr。

### 5.3 异步工作线程 (`workers/base_worker.py`)

*   **类名**: `BaseWorker(QThread)`
*   **职责**: 提供一个通用的异步任务执行框架，避免阻塞UI主线程。
*   **实现**:
    *   创建一个继承自 `QThread` 的基类。
    *   定义 `finished`, `progress`, `error` 等通用信号。
    *   在 `__init__` 方法中接收要执行的目标函数及其参数。
    *   在 `run` 方法中，使用 `try...except` 包裹目标函数的执行，并在适当的时候发射信号。
    *   **用法**: 在服务类中，实例化一个 `BaseWorker`，将耗时函数（如`wmi`查询）作为参数传入，连接其信号到UI更新槽函数，然后调用 `worker.start()`。
