#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Windows工具箱 - 主程序入口

集硬件信息查看、系统优化清理、预装应用卸载、超频工具管理及常用快捷工具于一体的现代化Windows桌面应用。
根据technical_documentation.md的设计规范实现。
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QIcon

# 导入自定义模块
from utils.privilege_manager import is_admin, require_admin
from utils.config_loader import get_loader


def setup_logging():
    """设置日志配置"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)

    log_file = log_dir / "windows_toolbox.log"

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(log_file, encoding="utf-8"),
            logging.StreamHandler(sys.stdout),
        ],
    )


def setup_application() -> QApplication:
    """
    设置应用程序基本配置

    Returns:
        QApplication: 配置好的应用程序实例
    """
    # 设置高DPI支持
    QApplication.setHighDpiScaleFactorRoundingPolicy(
        Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
    )

    # 创建应用程序实例
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("Windows工具箱")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Windows工具箱开发团队")
    app.setApplicationDisplayName("Windows工具箱")

    # 设置应用程序图标
    icon_path = project_root / "assets" / "icon.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))

    return app


def check_requirements() -> bool:
    """
    检查运行环境和依赖

    Returns:
        bool: 如果所有要求都满足返回True，否则返回False
    """
    logger = logging.getLogger(__name__)

    # 检查操作系统
    if sys.platform != "win32":
        logger.error("此应用程序仅支持Windows操作系统")
        QMessageBox.critical(None, "系统不支持", "此应用程序仅支持Windows操作系统。")
        return False

    # 检查Python版本
    if sys.version_info < (3, 8):
        logger.error(f"Python版本过低: {sys.version}")
        QMessageBox.critical(
            None, "Python版本过低", f"需要Python 3.8或更高版本，当前版本: {sys.version}"
        )
        return False

    # 检查必要的目录
    required_dirs = ["config", "assets", "interfaces", "services", "utils", "workers"]
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if not dir_path.exists():
            logger.error(f"缺少必要目录: {dir_path}")
            QMessageBox.critical(
                None, "目录缺失", f"缺少必要目录: {dir_name}\n请确保应用程序完整安装。"
            )
            return False

    logger.info("环境检查通过")
    return True


def show_splash_screen(app: QApplication) -> "SplashScreen":
    """
    显示启动画面

    Args:
        app: QApplication实例

    Returns:
        SplashScreen: 启动页面实例
    """
    from interfaces.splash_screen import create_splash_screen

    logger = logging.getLogger(__name__)

    # 创建临时图标（如果没有图标文件）
    icon = QIcon()
    try:
        icon = QIcon("assets/icon.ico")
    except Exception:
        logger.warning("无法加载应用图标，使用默认图标")

    # 创建启动页面
    splash = create_splash_screen(icon)

    # 显示启动页面并开始加载
    splash.show_with_loading(2000)  # 2秒加载时间

    logger.info("启动画面显示完成")
    return splash


def create_main_window():
    """创建主窗口"""
    logger = logging.getLogger(__name__)
    logger.info("创建主窗口")

    try:
        # 导入主窗口类
        from interfaces.main_window import MainWindow

        # 创建并显示主窗口
        main_window = MainWindow()
        main_window.show()

        # 显示欢迎信息
        main_window.show_info_message("欢迎使用", "Windows工具箱已成功启动！")

        logger.info("主窗口创建成功")
        return main_window

    except Exception as e:
        logger.error(f"主窗口创建失败: {str(e)}", exc_info=True)
        QMessageBox.critical(
            None,
            "主窗口创建失败",
            f"无法创建主窗口:\n{str(e)}\n\n请检查依赖是否正确安装。",
        )
        return None


def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("=" * 50)
    logger.info("Windows工具箱启动")
    logger.info("=" * 50)

    try:
        # 检查运行环境
        if not check_requirements():
            return 1

        # 检查管理员权限
        if not is_admin():
            logger.info("检测到需要管理员权限")
            reply = QMessageBox.question(
                None,
                "权限提升",
                "此应用程序需要管理员权限才能正常运行。\n是否要以管理员身份重新启动？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes,
            )

            if reply == QMessageBox.StandardButton.Yes:
                logger.info("用户同意权限提升，正在重新启动...")
                require_admin()
            else:
                logger.info("用户拒绝权限提升，程序退出")
                return 0

        logger.info("管理员权限检查通过")

        # 创建应用程序
        app = setup_application()

        # 加载配置
        logger.info("加载应用程序配置...")
        config_loader = get_loader()
        logger.info(
            f"配置加载完成，共加载 {len(config_loader.get_all_configs())} 个配置模块"
        )

        # 显示启动画面
        splash = show_splash_screen(app)

        # 创建主窗口
        main_window = create_main_window()
        if main_window is None:
            return 1

        # 等待启动画面完成后关闭
        splash.loadingFinished.connect(splash.close_splash)

        # 运行应用程序
        logger.info("应用程序开始运行")
        return app.exec()

    except Exception as e:
        logger.error(f"应用程序启动失败: {str(e)}", exc_info=True)
        QMessageBox.critical(
            None,
            "启动失败",
            f"应用程序启动失败:\n{str(e)}\n\n请查看日志文件获取详细信息。",
        )
        return 1

    finally:
        logger.info("应用程序退出")


if __name__ == "__main__":
    # 设置应用程序属性（在创建QApplication之前）
    os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"

    # 运行主程序
    exit_code = main()
    sys.exit(exit_code)
